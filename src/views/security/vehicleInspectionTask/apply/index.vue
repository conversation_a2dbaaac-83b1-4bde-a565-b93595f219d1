<template>
  <div class="vehicle-inspection-apply-form size-screen overflow-auto">
    <a-card class="mb-4">
      <template #title>
        <div class="w-full text-center font-bold text-primary-500">车辆检修申请工单</div>
      </template>

      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        class="-mt-4"
        @finish="handleSubmit"
      >
        <!-- 基本信息区块 -->
        <div
          class="bg-primary-50 !text-primary-500 rounded-md overflow-hidden px-4 py-2 mt-4 mb-8 font-bold"
          >基本信息</div
        >

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="工作票号" name="workTicketNo">
              <a-input
                v-model:value="formData.workTicketNo"
                placeholder="请输入工作票号（如 06-丄-15）"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工区" name="workArea">
              <a-input
                v-model:value="formData.workArea"
                placeholder="请输入工区（如 合肥南供电工区）"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="作业车" name="workVehicle">
              <a-input
                v-model:value="formData.workVehicle"
                placeholder="请输入作业车编号（如 1033038）"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 人员信息区块 -->
        <div
          class="bg-primary-50 !text-primary-500 rounded-md overflow-hidden px-4 py-2 mt-4 mb-8 font-bold"
          >人员信息</div
        >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="正驾驶" name="workStDriverId">
              <WarehouseWorkerSelect
                v-model:value="formData.workStDriverId"
                placeholder="请选择正驾驶"
                @change="(value, worker) => handleWorkerChange('workStDriver', value, worker)"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="副驾驶" name="workCoDriverId">
              <WarehouseWorkerSelect
                v-model:value="formData.workCoDriverId"
                placeholder="请选择副驾驶"
                @change="(value, worker) => handleWorkerChange('workCoDriver', value, worker)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="执行人" name="userId">
              <WarehouseWorkerSelect
                v-model:value="formData.userId"
                placeholder="请选择执行人"
                @change="(value, worker) => handleWorkerChange('executor', value, worker)"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 表单操作按钮 -->
        <div class="flex justify-center gap-4 mt-8">
          <a-button @click="resetForm" size="large" class="min-w-24">重置</a-button>
          <a-button
            type="primary"
            html-type="submit"
            :loading="submitLoading"
            size="large"
            class="min-w-24"
          >
            提交申请
          </a-button>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import WarehouseWorkerSelect from '../../warehouse/apply/components/WarehouseWorkerSelect.vue';
  import { addVehicleTask, type VehicleTaskForm, type WorkerInfo } from './api';

  // 表单引用
  const formRef = ref();
  const submitLoading = ref(false);

  // 表单数据
  const formData = reactive<VehicleTaskForm>({
    userId: void 0,
    userName: void 0,
    workArea: void 0,
    workVehicle: void 0,
    workStDriver: void 0,
    workStDriverId: void 0,
    workCoDriver: void 0,
    workCoDriverId: void 0,
    workTicketNo: void 0,
    workerNum: void 0,
  });

  // 表单验证规则
  const formRules = {
    workArea: [{ required: true, message: '请输入工区', trigger: 'blur' }],
    workVehicle: [{ required: true, message: '请输入作业车', trigger: 'blur' }],
    workTicketNo: [{ required: true, message: '请输入工作票号', trigger: 'blur' }],
    workStDriverId: [{ required: true, message: '请选择正驾驶', trigger: 'change' }],
    workCoDriverId: [{ required: true, message: '请选择副驾驶', trigger: 'change' }],
    userId: [{ required: true, message: '请选择执行人', trigger: 'change' }],
  };

  // 处理单个人员选择变化
  function handleWorkerChange(
    field: string,
    _value: string | number | string[] | number[] | undefined,
    worker: WorkerInfo | WorkerInfo[] | null,
  ) {
    // 确保是单个 WorkerInfo 对象
    if (worker && !Array.isArray(worker)) {
      if (field === 'workStDriver') {
        formData.workStDriver = worker.userName || '';
      } else if (field === 'workCoDriver') {
        formData.workCoDriver = worker.userName || '';
      } else if (field === 'executor') {
        formData.workerNum = worker.workerNum || '';
        formData.userName = worker.userName || '';
      }
    } else {
      if (field === 'workStDriver') {
        formData.workStDriver = '';
      } else if (field === 'workCoDriver') {
        formData.workCoDriver = '';
      } else if (field === 'executor') {
        formData.workerNum = '';
      }
    }
  }

  // 表单提交
  async function handleSubmit() {
    try {
      await formRef.value.validate();

      submitLoading.value = true;

      // 构建提交数据
      const submitData: VehicleTaskForm = {
        ...formData,
      };

      await addVehicleTask(submitData);

      message.success('车辆检修工单申请提交成功！');
      resetForm();
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('提交失败，请检查表单信息');
    } finally {
      submitLoading.value = false;
    }
  }

  // 重置表单
  function resetForm() {
    formRef.value.resetFields();
    Object.assign(formData, {
      userId: '',
      userName: '',
      workerNum: '',
      workArea: '',
      workVehicle: '',
      workStDriver: '',
      workStDriverId: '',
      workCoDriver: '',
      workCoDriverId: '',
      workTicketNo: '',
    });
  }
</script>

<style scoped>
  .vehicle-inspection-apply-form {
    padding: 16px;
    background-color: #f5f5f5;
  }

  .vehicle-inspection-apply-form .ant-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
  }
</style>
